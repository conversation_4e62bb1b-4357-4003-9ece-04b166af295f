from fastapi import FastAPI
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from loguru import logger


app = FastAPI()


class BookSchema(BaseModel):
    id: int
    title: str
    author: str
    description: Optional[str] = None
    rating: int


class CreateBookSchema(BaseModel):
    title: str = Field(min_length=3, max_length=30)
    author: str = Field(min_length=3, max_length=30)
    description: str = Field(min_length=1, max_length=100)
    rating: int = Field(gt=0, lt=6)


class ErrorSchema(BaseModel):
    error: str


BOOKS = [
    BookSchema(
        id=1, title="Book 1", author="Author 1", description="Description 1", rating=5
    ),
    BookSchema(
        id=2, title="Book 2", author="Author 2", description="Description 2", rating=4
    ),
    BookSchema(
        id=3, title="Book 3", author="Author 3", description="Description 3", rating=3
    ),
    BookSchema(
        id=4, title="Book 4", author="Author 4", description="Description 4", rating=2
    ),
    BookSchema(
        id=5, title="Book 5", author="Author 5", description="Description 5", rating=1
    ),
    BookSchema(
        id=6, title="Book 6", author="Author 6", description="Description 6", rating=5
    ),
]


@app.get("/", response_model=BookSchema)
async def root():
    return {"message": "Alawiye"}


@app.get("/books")
async def read_all_books():
    return BOOKS


@app.get("/books/book_id}")
async def read_book(book_id: int) -> Dict[str, Any]:
    for book in BOOKS:
        if book.id == book_id:
            return book
    return {"error": "Book not found"}


@app.post("/create-book")
async def create_book(book: CreateBookSchema):
    book = BookSchema(id=len(BOOKS) + 1, **book.dict())
    BOOKS.append(book)
    logger.info(BOOKS)
    return 201, book
